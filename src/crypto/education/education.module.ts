import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EducationController } from './education.controller';
import { EducationService } from './education.service';
import { UserRepository } from '../repositories/users.repository';
import { TransactionRepository } from '../repositories/transaction.repository';
import { User } from '../entities/user.entity';
import { Transaction } from '../entities/transactions.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Transaction]),
  ],
  controllers: [EducationController],
  providers: [
    EducationService,
    UserRepository,
    TransactionRepository,
  ],
  exports: [EducationService],
})
export class EducationModule {}
