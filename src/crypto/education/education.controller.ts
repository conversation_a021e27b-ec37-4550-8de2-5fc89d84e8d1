import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { EducationService } from './education.service';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard, GetUser } from '@crednet/authmanager';
import { AuthData } from '@crednet/authmanager';

@ApiTags('Education')
@Controller('education')
export class EducationController {
  constructor(private readonly educationService: EducationService) {}

  @Get('content')
  getEducationalContent(
    @Query('category') category: string,
    @Query('level') level: string,
  ) {
    return this.educationService.getEducationalContent(category, level);
  }

  @Get('recommended')
  @UseGuards(AuthGuard)
  @ApiBearerAuth('JWT')
  getRecommendedContent(@GetUser() auth: AuthData) {
    return this.educationService.getRecommendedContent(auth.id.toString());
  }

  @Get('content/:id')
  getContentById(@Param('id') id: string) {
    return this.educationService.getContentById(id);
  }

  @Get('progress')
  @UseGuards(AuthGuard)
  @ApiBearerAuth('JWT')
  getUserProgress(@GetUser() auth: AuthData) {
    return this.educationService.getUserProgress(auth.id.toString());
  }
}