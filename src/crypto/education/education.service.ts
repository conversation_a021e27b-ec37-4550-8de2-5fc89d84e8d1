import { Injectable } from '@nestjs/common';
import { ContentRepository } from '../repositories/content.repository';
import { UserRepository } from '../repositories/users.repository';

@Injectable()
export class EducationService {
  constructor(
    private readonly contentRepository: ContentRepository,
    private readonly userRepository: UserRepository,
  ) {}

  async getEducationalContent(category: string, level: string) {
    return this.contentRepository.find({
      where: { category, level },
      order: { order: 'ASC' },
    });
  }

  async getRecommendedContent(userId: string) {
    const user = await this.userRepository.getUserByUserId(userId);
    const userTransactions = await this.getUserTransactionHistory(user.id);
    
    // Determine user's experience level based on transaction history
    const experienceLevel = this.determineExperienceLevel(userTransactions);
    
    // Get content based on user's portfolio and experience
    return this.contentRepository.find({
      where: { level: experienceLevel },
      order: { relevance: 'DESC' },
      take: 5,
    });
  }

  private determineExperienceLevel(transactions) {
    const count = transactions.length;
    
    if (count < 5) return 'beginner';
    if (count < 20) return 'intermediate';
    return 'advanced';
  }
}