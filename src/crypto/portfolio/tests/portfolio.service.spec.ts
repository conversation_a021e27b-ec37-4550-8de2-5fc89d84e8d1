import { Test, TestingModule } from '@nestjs/testing';
import { PortfolioService } from '../portfolio.service';
import { WalletRepository } from '../../repositories/wallet.repository';
import { TransactionRepository } from '../../repositories/transaction.repository';
import { QuidaxService } from '@app/quidax';
import { UserRepository } from '../../repositories/users.repository';
import { PortfolioRepository } from '../../repositories/portfolio.repository';

describe('PortfolioService', () => {
  let service: PortfolioService;
  let walletRepository: WalletRepository;
  let userRepository: UserRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PortfolioService,
        {
          provide: WalletRepository,
          useValue: {
            getUserWallets: jest.fn(),
          },
        },
        {
          provide: TransactionRepository,
          useValue: {
            findByUser: jest.fn(),
          },
        },
        {
          provide: QuidaxService,
          useValue: {
            getMarketSummary: jest.fn(),
          },
        },
        {
          provide: UserRepository,
          useValue: {
            getUserByUserId: jest.fn(),
          },
        },
        {
          provide: PortfolioRepository,
          useValue: {
            getHistoricalData: jest.fn(),
            saveSnapshot: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PortfolioService>(PortfolioService);
    walletRepository = module.get<WalletRepository>(WalletRepository);
    userRepository = module.get<UserRepository>(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getPortfolioOverview', () => {
    it('should return portfolio overview for a user', async () => {
      // Mock data
      const userId = '123';
      const user = { id: '456', userId };
      const wallets = [
        { currency: 'btc', balance: '0.5', convertedBalance: '10000' },
        { currency: 'eth', balance: '2', convertedBalance: '5000' },
      ];
      const historicalData = [
        { timestamp: new Date(), totalValue: '14000' },
        { timestamp: new Date(Date.now() - 86400000), totalValue: '13500' },
      ];

      // Mock repository responses
      jest.spyOn(userRepository, 'getUserByUserId').mockResolvedValue(user);
      jest.spyOn(walletRepository, 'getUserWallets').mockResolvedValue(wallets);
      jest.spyOn(service['portfolioRepository'], 'getHistoricalData').mockResolvedValue(historicalData);

      // Execute
      const result = await service.getPortfolioOverview({ id: userId } as any);

      // Assert
      expect(userRepository.getUserByUserId).toHaveBeenCalledWith(userId);
      expect(walletRepository.getUserWallets).toHaveBeenCalledWith(user.id);
      expect(result.totalValue).toBe(15000);
      expect(result.wallets).toEqual(wallets);
      expect(result.historicalData).toEqual(historicalData);
      expect(result.assetAllocation).toHaveLength(2);
      expect(result.assetAllocation[0].percentage).toBe(66.67);
      expect(result.assetAllocation[1].percentage).toBe(33.33);
    });

    it('should throw error if user not found', async () => {
      // Mock repository responses
      jest.spyOn(userRepository, 'getUserByUserId').mockResolvedValue(null);

      // Execute and assert
      await expect(service.getPortfolioOverview({ id: '123' } as any)).rejects.toThrow('User not found');
    });
  });
});