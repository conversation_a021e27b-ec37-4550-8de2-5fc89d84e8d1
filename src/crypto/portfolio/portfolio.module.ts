import { Modu<PERSON> } from '@nestjs/common';
import { PortfolioService } from './portfolio.service';
import { PortfolioController } from './portfolio.controller';
import { WalletRepository } from '../repositories/wallet.repository';
import { TransactionRepository } from '../repositories/transaction.repository';
import { QuidaxModule } from '@app/quidax';
import { UserRepository } from '../repositories/users.repository';
import { PortfolioRepository } from '../repositories/portfolio.repository';

@Module({
  imports: [QuidaxModule],
  controllers: [PortfolioController],
  providers: [
    PortfolioService,
    PortfolioRepository,
    WalletRepository,
    TransactionRepository,
    UserRepository,
  ],
})
export class PortfolioModule {}