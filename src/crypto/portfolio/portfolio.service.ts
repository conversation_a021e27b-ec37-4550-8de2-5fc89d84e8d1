import { Injectable } from '@nestjs/common';
import { WalletRepository } from '../repositories/wallet.repository';
import { TransactionRepository } from '../repositories/transaction.repository';
import { QuidaxService } from '@app/quidax';
import { AuthData } from '@crednet/authmanager';
import { UserRepository } from '../repositories/users.repository';
import { PortfolioRepository } from '../repositories/portfolio.repository';

@Injectable()
export class PortfolioService {
  constructor(
    private readonly walletRepository: WalletRepository,
    private readonly transactionRepository: TransactionRepository,
    private readonly quidaxService: QuidaxService,
    private readonly userRepository: UserRepository,
    private readonly portfolioRepository: PortfolioRepository,
  ) {}

  async getPortfolioOverview(auth: AuthData) {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new Error('User not found');
    }

    const wallets = await this.walletRepository.getUserWallets(user.id);
    const totalValue = await this.calculatePortfolioValue(wallets);
    
    // Get historical data for portfolio performance
    const historicalData = await this.portfolioRepository.getHistoricalData(user.id);
    
    return {
      totalValue,
      wallets,
      historicalData,
      assetAllocation: this.calculateAssetAllocation(wallets, totalValue),
    };
  }

  private calculateAssetAllocation(wallets, totalValue) {
    return wallets.map(wallet => ({
      currency: wallet.currency,
      percentage: (parseFloat(wallet.convertedBalance) / totalValue) * 100,
      value: parseFloat(wallet.convertedBalance)
    }));
  }

  private async calculatePortfolioValue(wallets) {
    return wallets.reduce((total, wallet) => 
      total + parseFloat(wallet.convertedBalance), 0);
  }
}