import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { NotificationRepository } from '../repositories/notification.repository';
import { UserRepository } from '../repositories/users.repository';
import { WebsocketGateway } from './websocket.gateway';
import { NotificationType } from '../entities/notification.entity';

@Injectable()
export class NotificationsService {
  constructor(
    private readonly notificationRepository: NotificationRepository,
    private readonly userRepository: UserRepository,
    private readonly websocketGateway: WebsocketGateway,
  ) {}

  async createNotification(
    userId: string,
    type: NotificationType,
    title: string,
    message: string,
    data?: any,
  ) {
    const user = await this.userRepository.getUserByUserId(userId);

    const notification = await this.notificationRepository.save({
      user,
      type,
      title,
      message,
      data,
      isRead: false,
    });

    // Send real-time notification
    this.websocketGateway.sendNotificationToUser(userId, notification);

    return notification;
  }

  @OnEvent('transaction.completed')
  handleTransactionCompleted(payload: any) {
    return this.createNotification(
      payload.userId,
      NotificationType.TRANSACTION_COMPLETED,
      'Transaction Completed',
      `Your ${payload.currency} transaction has been completed successfully.`,
      {
        transactionId: payload.transactionId,
        amount: payload.amount,
        currency: payload.currency,
      },
    );
  }

  @OnEvent('price.alert')
  handlePriceAlert(payload: any) {
    return this.createNotification(
      payload.userId,
      NotificationType.PRICE_ALERT,
      'Price Alert',
      `${payload.currency} has reached your target price of ${payload.price}.`,
      {
        currency: payload.currency,
        price: payload.price,
        condition: payload.condition,
      },
    );
  }
}
