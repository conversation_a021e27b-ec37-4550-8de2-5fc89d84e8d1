import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { NotificationRepository } from '../repositories/notification.repository';
import { UserRepository } from '../repositories/users.repository';
import { WebsocketGateway } from './websocket.gateway';

@Injectable()
export class NotificationsService {
  constructor(
    private readonly notificationRepository: NotificationRepository,
    private readonly userRepository: UserRepository,
    private readonly websocketGateway: WebsocketGateway,
  ) {}

  async createNotification(userId: string, type: string, data: any) {
    const notification = await this.notificationRepository.save({
      userId,
      type,
      data,
      isRead: false,
      createdAt: new Date(),
    });
    
    // Send real-time notification
    this.websocketGateway.sendNotificationToUser(userId, notification);
    
    return notification;
  }

  @OnEvent('transaction.completed')
  handleTransactionCompleted(payload: any) {
    return this.createNotification(
      payload.userId,
      'transaction_completed',
      {
        transactionId: payload.transactionId,
        amount: payload.amount,
        currency: payload.currency,
      },
    );
  }

  @OnEvent('price.alert')
  handlePriceAlert(payload: any) {
    return this.createNotification(
      payload.userId,
      'price_alert',
      {
        currency: payload.currency,
        price: payload.price,
        condition: payload.condition,
      },
    );
  }
}