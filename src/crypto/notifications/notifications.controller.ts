import { <PERSON>, <PERSON>, Post, <PERSON>, Param, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard, GetAuthData } from '@crednet/authmanager';
import { AuthData } from '@crednet/authmanager';
import { NotificationsService } from './notifications.service';

@Controller('notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('Notifications')
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Get()
  @ApiOperation({ summary: 'Get user notifications' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  async getUserNotifications(@GetAuthData() auth: AuthData) {
    // This would typically call a service method to get notifications
    return {
      notifications: [],
      unreadCount: 0,
    };
  }

  @Patch(':id/read')
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  async markAsRead(
    @Param('id') notificationId: string,
    @GetAuthData() auth: AuthData,
  ) {
    // This would typically call a service method to mark as read
    return { message: 'Notification marked as read' };
  }

  @Post('mark-all-read')
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({ status: 200, description: 'All notifications marked as read' })
  async markAllAsRead(@GetAuthData() auth: AuthData) {
    // This would typically call a service method to mark all as read
    return { message: 'All notifications marked as read' };
  }
}
