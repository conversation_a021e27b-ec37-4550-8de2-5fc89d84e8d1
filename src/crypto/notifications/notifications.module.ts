import { <PERSON>du<PERSON> } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationsController } from './notifications.controller';
import { NotificationRepository } from '../repositories/notification.repository';
import { UserRepository } from '../repositories/users.repository';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { WebsocketGateway } from './websocket.gateway';

@Module({
  imports: [EventEmitterModule.forRoot()],
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    NotificationRepository,
    UserRepository,
    WebsocketGateway,
  ],
  exports: [NotificationsService],
})
export class NotificationsModule {}