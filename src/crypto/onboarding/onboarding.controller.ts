import { Controller, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { OnboardingService } from './onboarding.service';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard, GetUser } from '@crednet/authmanager';
import { AuthData } from '@crednet/authmanager';

@ApiTags('Onboarding')
@Controller('onboarding')
@UseGuards(AuthGuard)
@ApiBearerAuth('JWT')
export class OnboardingController {
  constructor(private readonly onboardingService: OnboardingService) {}

  @Get('status')
  getOnboardingStatus(@GetUser() auth: AuthData) {
    return this.onboardingService.getOnboardingStatus(auth);
  }

  @Post('step/:step')
  completeOnboardingStep(
    @Param('step') step: string,
    @Body() data: any,
    @GetUser() auth: AuthData,
  ) {
    return this.onboardingService.completeOnboardingStep(auth, step, data);
  }

  @Get('recommendations')
  getOnboardingRecommendations(@GetUser() auth: AuthData) {
    return this.onboardingService.getOnboardingRecommendations(auth);
  }
}