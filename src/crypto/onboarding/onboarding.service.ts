import { Injectable } from '@nestjs/common';
import { UserRepository } from '../repositories/users.repository';
import { QuidaxService } from '@app/quidax';
import { AuthData } from '@crednet/authmanager';

@Injectable()
export class OnboardingService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly quidaxService: QuidaxService,
  ) {}

  async getOnboardingStatus(auth: AuthData) {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    
    return {
      isRegistered: !!user,
      hasCompletedKyc: user?.kycVerified || false,
      hasCreatedWallet: user?.hasWallet || false,
      hasCompletedFirstTransaction: user?.hasTransaction || false,
    };
  }

  async completeOnboardingStep(auth: AuthData, step: string, data: any) {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    
    switch (step) {
      case 'create_wallet':
        // Create default wallets for popular cryptocurrencies
        await this.createDefaultWallets(user.id);
        break;
      case 'investment_preferences':
        // Save user investment preferences
        await this.saveInvestmentPreferences(user.id, data);
        break;
      // Other onboarding steps
    }
    
    return this.getOnboardingStatus(auth);
  }

  private async createDefaultWallets(userId: string) {
    const defaultCurrencies = ['btc', 'eth', 'usdt'];
    
    for (const currency of defaultCurrencies) {
      await this.quidaxService.fetchWallet(userId, currency);
    }
    
    await this.userRepository.update(
      { id: userId },
      { hasWallet: true },
    );
  }
}