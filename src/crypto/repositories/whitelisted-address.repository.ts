import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { WhitelistedAddress } from '../entities/whitelisted-address.entity';

@Injectable()
export class WhitelistedAddressRepository extends TypeOrmRepository<WhitelistedAddress> {
  constructor(private readonly dataSource: DataSource) {
    super(WhitelistedAddress, dataSource.createEntityManager());
  }

  async findByUserAndCurrency(userId: string, currency: string): Promise<WhitelistedAddress[]> {
    return this.find({
      where: {
        user: { userId },
        currency,
        isActive: true,
      },
      relations: ['user'],
    });
  }

  async findByUserAndAddress(userId: string, address: string): Promise<WhitelistedAddress | null> {
    return this.findOne({
      where: {
        user: { userId },
        address,
        isActive: true,
      },
      relations: ['user'],
    });
  }

  async verifyAddress(id: string): Promise<void> {
    await this.update(id, {
      isVerified: true,
      verifiedAt: new Date(),
      verificationCode: null,
    });
  }
}
