import { Controller, Get, Post, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard, GetAuthData } from '@crednet/authmanager';
import { AuthData } from '@crednet/authmanager';
import { NftService } from './nft.service';

@Controller('nft')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('NFT Portfolio')
export class NftController {
  constructor(private readonly nftService: NftService) {}

  @Get('portfolio')
  @ApiOperation({ summary: 'Get user NFT portfolio overview' })
  @ApiResponse({ status: 200, description: 'NFT portfolio retrieved successfully' })
  async getUserNftPortfolio(@GetAuthData() auth: AuthData) {
    return this.nftService.getUserNftPortfolio(auth);
  }

  @Get('collections')
  @ApiOperation({ summary: 'Get user NFT collections' })
  @ApiResponse({ status: 200, description: 'NFT collections retrieved successfully' })
  async getUserCollections(@GetAuthData() auth: AuthData) {
    return this.nftService.getUserCollections(auth);
  }

  @Get()
  @ApiOperation({ summary: 'Get user NFTs' })
  @ApiQuery({ name: 'collection', required: false, description: 'Filter by collection slug' })
  @ApiResponse({ status: 200, description: 'NFTs retrieved successfully' })
  async getUserNfts(
    @GetAuthData() auth: AuthData,
    @Query('collection') collectionSlug?: string,
  ) {
    return this.nftService.getUserNfts(auth, collectionSlug);
  }

  @Get('search')
  @ApiOperation({ summary: 'Search user NFTs' })
  @ApiQuery({ name: 'q', required: true, description: 'Search query' })
  @ApiResponse({ status: 200, description: 'NFT search results retrieved successfully' })
  async searchNfts(
    @GetAuthData() auth: AuthData,
    @Query('q') query: string,
  ) {
    return this.nftService.searchNfts(auth, query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get NFT details' })
  @ApiResponse({ status: 200, description: 'NFT details retrieved successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found' })
  async getNftDetails(
    @GetAuthData() auth: AuthData,
    @Param('id') nftId: string,
  ) {
    return this.nftService.getNftDetails(auth, nftId);
  }

  @Post()
  @ApiOperation({ summary: 'Add NFT to portfolio' })
  @ApiResponse({ status: 201, description: 'NFT added to portfolio successfully' })
  @ApiResponse({ status: 400, description: 'Invalid NFT data or NFT already exists' })
  async addNftToPortfolio(
    @GetAuthData() auth: AuthData,
    @Body() nftData: any,
  ) {
    return this.nftService.addNftToPortfolio(auth, nftData);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remove NFT from portfolio' })
  @ApiResponse({ status: 200, description: 'NFT removed from portfolio successfully' })
  @ApiResponse({ status: 404, description: 'NFT not found' })
  async removeNftFromPortfolio(
    @GetAuthData() auth: AuthData,
    @Param('id') nftId: string,
  ) {
    await this.nftService.removeNftFromPortfolio(auth, nftId);
    return { message: 'NFT removed from portfolio successfully' };
  }
}
