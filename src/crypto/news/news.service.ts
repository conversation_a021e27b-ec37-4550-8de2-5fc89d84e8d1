import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

export interface NewsArticle {
  id: string;
  title: string;
  description: string;
  content: string;
  url: string;
  imageUrl?: string;
  publishedAt: Date;
  source: string;
  author?: string;
  category: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
  relevantCoins?: string[];
}

export interface MarketSentiment {
  overall: 'bullish' | 'bearish' | 'neutral';
  score: number; // -100 to 100
  fearGreedIndex?: number;
  topMentions: Array<{
    coin: string;
    mentions: number;
    sentiment: 'positive' | 'negative' | 'neutral';
  }>;
}

@Injectable()
export class NewsService {
  private readonly logger = new Logger(NewsService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async getLatestNews(
    category?: string,
    limit: number = 20,
    offset: number = 0,
  ): Promise<NewsArticle[]> {
    try {
      // In a real implementation, this would integrate with news APIs like:
      // - CoinDesk API
      // - CryptoNews API
      // - NewsAPI with crypto keywords
      // - CoinTelegraph API
      
      // Mock data for demonstration
      const mockNews: NewsArticle[] = [
        {
          id: '1',
          title: 'Bitcoin Reaches New All-Time High Amid Institutional Adoption',
          description: 'Bitcoin surged to unprecedented levels as major corporations announce crypto treasury strategies.',
          content: 'Full article content would be here...',
          url: 'https://example.com/news/1',
          imageUrl: 'https://example.com/images/bitcoin-ath.jpg',
          publishedAt: new Date('2024-01-15T10:30:00Z'),
          source: 'CryptoNews',
          author: 'John Doe',
          category: 'market',
          sentiment: 'positive',
          relevantCoins: ['BTC', 'ETH'],
        },
        {
          id: '2',
          title: 'Ethereum 2.0 Staking Rewards Hit Record Levels',
          description: 'Ethereum staking yields reach new highs as network activity increases.',
          content: 'Full article content would be here...',
          url: 'https://example.com/news/2',
          imageUrl: 'https://example.com/images/eth-staking.jpg',
          publishedAt: new Date('2024-01-15T09:15:00Z'),
          source: 'DeFi Daily',
          author: 'Jane Smith',
          category: 'defi',
          sentiment: 'positive',
          relevantCoins: ['ETH'],
        },
        {
          id: '3',
          title: 'Regulatory Clarity Boosts Crypto Market Confidence',
          description: 'New regulatory framework provides clearer guidelines for crypto operations.',
          content: 'Full article content would be here...',
          url: 'https://example.com/news/3',
          publishedAt: new Date('2024-01-15T08:45:00Z'),
          source: 'Regulatory Watch',
          category: 'regulation',
          sentiment: 'positive',
          relevantCoins: ['BTC', 'ETH', 'ADA'],
        },
      ];

      // Filter by category if provided
      let filteredNews = mockNews;
      if (category) {
        filteredNews = mockNews.filter(article => article.category === category);
      }

      // Apply pagination
      const paginatedNews = filteredNews.slice(offset, offset + limit);

      this.logger.log(`Retrieved ${paginatedNews.length} news articles`);
      return paginatedNews;

    } catch (error) {
      this.logger.error('Failed to fetch news:', error);
      return [];
    }
  }

  async getNewsByCategory(category: string, limit: number = 10): Promise<NewsArticle[]> {
    return this.getLatestNews(category, limit);
  }

  async searchNews(query: string, limit: number = 10): Promise<NewsArticle[]> {
    try {
      const allNews = await this.getLatestNews(undefined, 100);
      
      // Simple search implementation
      const searchResults = allNews.filter(article =>
        article.title.toLowerCase().includes(query.toLowerCase()) ||
        article.description.toLowerCase().includes(query.toLowerCase()) ||
        article.relevantCoins?.some(coin => 
          coin.toLowerCase().includes(query.toLowerCase())
        )
      );

      return searchResults.slice(0, limit);
    } catch (error) {
      this.logger.error('Failed to search news:', error);
      return [];
    }
  }

  async getMarketSentiment(): Promise<MarketSentiment> {
    try {
      // In a real implementation, this would analyze news sentiment using:
      // - Natural Language Processing APIs
      // - Social media sentiment analysis
      // - Fear & Greed Index APIs
      // - Trading volume and price action correlation

      // Mock sentiment data
      const sentiment: MarketSentiment = {
        overall: 'bullish',
        score: 75,
        fearGreedIndex: 68,
        topMentions: [
          {
            coin: 'BTC',
            mentions: 1250,
            sentiment: 'positive',
          },
          {
            coin: 'ETH',
            mentions: 890,
            sentiment: 'positive',
          },
          {
            coin: 'ADA',
            mentions: 456,
            sentiment: 'neutral',
          },
          {
            coin: 'SOL',
            mentions: 234,
            sentiment: 'positive',
          },
        ],
      };

      this.logger.log('Market sentiment analysis completed');
      return sentiment;

    } catch (error) {
      this.logger.error('Failed to analyze market sentiment:', error);
      return {
        overall: 'neutral',
        score: 0,
        topMentions: [],
      };
    }
  }

  async getNewsForCoin(coinSymbol: string, limit: number = 10): Promise<NewsArticle[]> {
    try {
      const allNews = await this.getLatestNews(undefined, 100);
      
      const coinNews = allNews.filter(article =>
        article.relevantCoins?.includes(coinSymbol.toUpperCase()) ||
        article.title.toLowerCase().includes(coinSymbol.toLowerCase()) ||
        article.description.toLowerCase().includes(coinSymbol.toLowerCase())
      );

      return coinNews.slice(0, limit);
    } catch (error) {
      this.logger.error(`Failed to fetch news for ${coinSymbol}:`, error);
      return [];
    }
  }

  async getTrendingTopics(): Promise<Array<{ topic: string; mentions: number; sentiment: string }>> {
    try {
      // Mock trending topics data
      const trendingTopics = [
        { topic: 'Bitcoin ETF', mentions: 2340, sentiment: 'positive' },
        { topic: 'Ethereum Merge', mentions: 1890, sentiment: 'positive' },
        { topic: 'DeFi Yields', mentions: 1456, sentiment: 'neutral' },
        { topic: 'NFT Market', mentions: 1123, sentiment: 'negative' },
        { topic: 'Stablecoin Regulation', mentions: 987, sentiment: 'neutral' },
        { topic: 'Layer 2 Solutions', mentions: 765, sentiment: 'positive' },
        { topic: 'Crypto Mining', mentions: 654, sentiment: 'neutral' },
        { topic: 'Web3 Gaming', mentions: 543, sentiment: 'positive' },
      ];

      this.logger.log('Trending topics retrieved');
      return trendingTopics;

    } catch (error) {
      this.logger.error('Failed to fetch trending topics:', error);
      return [];
    }
  }

  async getNewsCategories(): Promise<Array<{ name: string; count: number }>> {
    return [
      { name: 'market', count: 45 },
      { name: 'defi', count: 32 },
      { name: 'regulation', count: 28 },
      { name: 'technology', count: 24 },
      { name: 'nft', count: 18 },
      { name: 'mining', count: 15 },
      { name: 'adoption', count: 12 },
      { name: 'security', count: 8 },
    ];
  }

  private analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    // Simple sentiment analysis - in production, use proper NLP services
    const positiveWords = ['bullish', 'surge', 'gain', 'rise', 'positive', 'growth', 'adoption'];
    const negativeWords = ['bearish', 'crash', 'fall', 'decline', 'negative', 'loss', 'dump'];
    
    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private extractRelevantCoins(text: string): string[] {
    const coinSymbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'AAVE', 'USDT', 'USDC'];
    const lowerText = text.toLowerCase();
    
    return coinSymbols.filter(coin => 
      lowerText.includes(coin.toLowerCase()) || 
      lowerText.includes(this.getCoinName(coin).toLowerCase())
    );
  }

  private getCoinName(symbol: string): string {
    const coinNames: { [key: string]: string } = {
      'BTC': 'bitcoin',
      'ETH': 'ethereum',
      'ADA': 'cardano',
      'SOL': 'solana',
      'DOT': 'polkadot',
      'LINK': 'chainlink',
      'UNI': 'uniswap',
      'AAVE': 'aave',
      'USDT': 'tether',
      'USDC': 'usd coin',
    };
    
    return coinNames[symbol] || symbol;
  }
}
