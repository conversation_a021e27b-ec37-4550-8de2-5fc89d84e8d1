import { Controller, Post, Body, Get, Param, Delete, UseGuards } from '@nestjs/common';
import { RecurringBuysService } from './recurring-buys.service';
import { CreateRecurringBuyDto } from './dtos/recurring-buy.dto';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard, GetUser } from '@crednet/authmanager';
import { AuthData } from '@crednet/authmanager';

@ApiTags('Recurring Buys')
@Controller('recurring-buys')
@UseGuards(AuthGuard)
@ApiBearerAuth('JWT')
export class RecurringBuysController {
  constructor(private readonly recurringBuysService: RecurringBuysService) {}

  @Post()
  createRecurringBuy(
    @Body() createRecurringBuyDto: CreateRecurringBuyDto,
    @GetUser() auth: AuthData,
  ) {
    return this.recurringBuysService.createRecurringBuy(
      auth.id.toString(),
      createRecurringBuyDto,
    );
  }

  @Get()
  getUserRecurringBuys(@GetUser() auth: AuthData) {
    return this.recurringBuysService.getUserRecurringBuys(auth.id.toString());
  }

  @Get(':id')
  getRecurringBuy(@Param('id') id: string, @GetUser() auth: AuthData) {
    return this.recurringBuysService.getRecurringBuy(id, auth.id.toString());
  }

  @Delete(':id')
  cancelRecurringBuy(@Param('id') id: string, @GetUser() auth: AuthData) {
    return this.recurringBuysService.cancelRecurringBuy(id, auth.id.toString());
  }
}