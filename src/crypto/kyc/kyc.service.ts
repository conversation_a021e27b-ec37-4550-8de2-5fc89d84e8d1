import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { UserRepository } from '../repositories/users.repository';
import { QuidaxService } from '@app/quidax';
import { AuthData } from '@crednet/authmanager';
import { KycStatus } from '../entities/user.entity';
import { EventEmitter2 } from '@nestjs/event-emitter';

export interface KycDocumentDto {
  documentType: 'passport' | 'drivers_license' | 'national_id';
  documentNumber: string;
  documentImage: string; // base64 encoded image
  selfieImage: string; // base64 encoded selfie
  address?: string;
  dateOfBirth?: Date;
}

export interface KycVerificationResult {
  isVerified: boolean;
  confidence: number;
  reasons?: string[];
}

@Injectable()
export class KycService {
  private readonly logger = new Logger(KycService.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly quidaxService: QuidaxService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async submitKycDocuments(auth: AuthData, kycData: KycDocumentDto): Promise<any> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.kycStatus === KycStatus.VERIFIED) {
      throw new BadRequestException('KYC already verified');
    }

    try {
      // Submit to Quidax for verification
      const quidaxResult = await this.quidaxService.submitKycDocuments(auth.id.toString(), {
        document_type: kycData.documentType,
        document_number: kycData.documentNumber,
        document_image: kycData.documentImage,
        selfie_image: kycData.selfieImage,
        address: kycData.address,
        date_of_birth: kycData.dateOfBirth,
      });

      // Update user status
      await this.userRepository.update(
        { id: user.id },
        {
          kycStatus: KycStatus.UNDER_REVIEW,
          kycSubmittedAt: new Date(),
        },
      );

      // Emit event for notifications
      this.eventEmitter.emit('kyc.submitted', {
        userId: user.id,
        submittedAt: new Date(),
      });

      this.logger.log(`KYC documents submitted for user ${user.id}`);

      return {
        status: 'submitted',
        message: 'KYC documents submitted successfully. Review typically takes 1-3 business days.',
        referenceId: quidaxResult.reference_id,
      };
    } catch (error) {
      this.logger.error(`KYC submission failed for user ${user.id}:`, error);
      throw new BadRequestException('Failed to submit KYC documents. Please try again.');
    }
  }

  async getKycStatus(auth: AuthData): Promise<any> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    return {
      status: user.kycStatus,
      isVerified: user.kycVerified,
      submittedAt: user.kycSubmittedAt,
      verifiedAt: user.kycVerifiedAt,
      rejectionReason: user.kycRejectionReason,
    };
  }

  async verifyKyc(userId: string, verificationResult: KycVerificationResult): Promise<void> {
    const user = await this.userRepository.getUserByUserId(userId);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (verificationResult.isVerified) {
      await this.userRepository.update(
        { id: user.id },
        {
          kycStatus: KycStatus.VERIFIED,
          kycVerified: true,
          kycVerifiedAt: new Date(),
          kycRejectionReason: null,
        },
      );

      // Emit event for notifications
      this.eventEmitter.emit('kyc.verified', {
        userId: user.id,
        verifiedAt: new Date(),
      });

      this.logger.log(`KYC verified for user ${user.id}`);
    } else {
      await this.userRepository.update(
        { id: user.id },
        {
          kycStatus: KycStatus.REJECTED,
          kycVerified: false,
          kycRejectionReason: verificationResult.reasons?.join(', ') || 'Verification failed',
        },
      );

      // Emit event for notifications
      this.eventEmitter.emit('kyc.rejected', {
        userId: user.id,
        rejectionReason: verificationResult.reasons?.join(', '),
      });

      this.logger.log(`KYC rejected for user ${user.id}: ${verificationResult.reasons?.join(', ')}`);
    }
  }

  async checkKycRequired(userId: string): Promise<boolean> {
    const user = await this.userRepository.getUserByUserId(userId);
    return user ? !user.kycVerified : true;
  }

  async getKycRequirements(): Promise<any> {
    return {
      requiredDocuments: [
        {
          type: 'government_id',
          description: 'Valid government-issued photo ID (passport, driver\'s license, or national ID)',
          required: true,
        },
        {
          type: 'selfie',
          description: 'Clear selfie photo holding your ID document',
          required: true,
        },
        {
          type: 'address_proof',
          description: 'Proof of address (utility bill, bank statement, etc.)',
          required: false,
        },
      ],
      guidelines: [
        'Ensure all text on documents is clearly visible',
        'Photos should be well-lit and in focus',
        'Documents must be valid and not expired',
        'Selfie should clearly show your face and the ID document',
      ],
    };
  }
}
