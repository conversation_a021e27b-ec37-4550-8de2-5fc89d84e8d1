import { Injectable } from '@nestjs/common';
import { QuidaxService } from '@app/quidax';
import { DcaStrategyRepository } from '../repositories/dca-strategy.repository';
import { UserRepository } from '../repositories/users.repository';
import { Cron, CronExpression } from '@nestjs/schedule';
import { randomUUID } from 'crypto';

@Injectable()
export class DcaService {
  constructor(
    private readonly dcaStrategyRepository: DcaStrategyRepository,
    private readonly quidaxService: QuidaxService,
    private readonly userRepository: UserRepository,
  ) {}

  async createDcaStrategy(userId: string, dcaStrategyDto) {
    const user = await this.userRepository.getUserByUserId(userId);
    
    return this.dcaStrategyRepository.save({
      user,
      totalAmount: dcaStrategyDto.totalAmount,
      currency: dcaStrategyDto.currency,
      intervalAmount: dcaStrategyDto.intervalAmount,
      frequency: dcaStrategyDto.frequency,
      nextExecutionDate: this.calculateNextExecutionDate(dcaStrategyDto.frequency),
      isActive: true,
      remainingAmount: dcaStrategyDto.totalAmount,
    });
  }

  @Cron(CronExpression.EVERY_HOUR)
  async executeDcaStrategies() {
    const activeStrategies = await this.dcaStrategyRepository.findActiveStrategies();
    
    for (const strategy of activeStrategies) {
      if (new Date() >= strategy.nextExecutionDate && strategy.remainingAmount > 0) {
        try {
          const amountToInvest = Math.min(strategy.intervalAmount, strategy.remainingAmount);
          
          // Execute the buy order
          const reference = 'cp_dca_' + randomUUID();
          await this.quidaxService.createOrder(strategy.user.id, {
            market: strategy.currency + 'ngn',
            side: 'buy',
            volume: amountToInvest,
            ord_type: 'market',
            reference,
          });
          
          // Update strategy
          strategy.remainingAmount -= amountToInvest;
          strategy.nextExecutionDate = this.calculateNextExecutionDate(strategy.frequency);
          await this.dcaStrategyRepository.save(strategy);
          
          // If strategy is complete, mark as inactive
          if (strategy.remainingAmount <= 0) {
            strategy.isActive = false;
            await this.dcaStrategyRepository.save(strategy);
          }
        } catch (error) {
          console.error(`Failed to execute DCA strategy for user ${strategy.user.id}:`, error);
        }
      }
    }
  }

  private calculateNextExecutionDate(frequency, fromDate = new Date()) {
    // Implementation to calculate next execution date based on frequency
  }
}