import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SecurityService } from './security.service';
import { UserRepository } from '../repositories/users.repository';
import { WhitelistedAddressRepository } from '../repositories/whitelisted-address.repository';
import { User } from '../entities/user.entity';
import { WhitelistedAddress } from '../entities/whitelisted-address.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, WhitelistedAddress]),
  ],
  providers: [
    SecurityService,
    UserRepository,
    WhitelistedAddressRepository,
  ],
  exports: [SecurityService],
})
export class SecurityModule {}
