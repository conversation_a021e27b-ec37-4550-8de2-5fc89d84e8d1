import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { UserRepository } from '../repositories/users.repository';
import { AuthData } from '@crednet/authmanager';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';

export interface TwoFactorSetupDto {
  token: string;
}

export interface TwoFactorVerifyDto {
  token: string;
}

@Injectable()
export class TwoFactorService {
  private readonly logger = new Logger(TwoFactorService.name);

  constructor(private readonly userRepository: UserRepository) {}

  async generateSecret(auth: AuthData): Promise<any> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.twoFactorEnabled) {
      throw new BadRequestException('Two-factor authentication is already enabled');
    }

    const secret = speakeasy.generateSecret({
      name: `CryptoService (${user.email})`,
      issuer: 'CryptoService',
      length: 32,
    });

    // Store temporary secret
    await this.userRepository.update(
      { id: user.id },
      { tempTwoFactorSecret: secret.base32 },
    );

    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

    this.logger.log(`2FA secret generated for user ${user.id}`);

    return {
      secret: secret.base32,
      qrCode: qrCodeUrl,
      manualEntryKey: secret.base32,
    };
  }

  async enableTwoFactor(auth: AuthData, dto: TwoFactorSetupDto): Promise<any> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.twoFactorEnabled) {
      throw new BadRequestException('Two-factor authentication is already enabled');
    }

    if (!user.tempTwoFactorSecret) {
      throw new BadRequestException('No temporary secret found. Please generate a new secret first.');
    }

    // Verify the token
    const verified = speakeasy.totp.verify({
      secret: user.tempTwoFactorSecret,
      encoding: 'base32',
      token: dto.token,
      window: 2,
    });

    if (!verified) {
      throw new BadRequestException('Invalid verification code');
    }

    // Enable 2FA
    await this.userRepository.update(
      { id: user.id },
      {
        twoFactorEnabled: true,
        twoFactorSecret: user.tempTwoFactorSecret,
        tempTwoFactorSecret: null,
      },
    );

    this.logger.log(`2FA enabled for user ${user.id}`);

    return {
      message: 'Two-factor authentication has been successfully enabled',
      enabled: true,
    };
  }

  async disableTwoFactor(auth: AuthData, dto: TwoFactorVerifyDto): Promise<any> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (!user.twoFactorEnabled) {
      throw new BadRequestException('Two-factor authentication is not enabled');
    }

    // Verify the token
    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token: dto.token,
      window: 2,
    });

    if (!verified) {
      throw new BadRequestException('Invalid verification code');
    }

    // Disable 2FA
    await this.userRepository.update(
      { id: user.id },
      {
        twoFactorEnabled: false,
        twoFactorSecret: null,
        tempTwoFactorSecret: null,
      },
    );

    this.logger.log(`2FA disabled for user ${user.id}`);

    return {
      message: 'Two-factor authentication has been successfully disabled',
      enabled: false,
    };
  }

  async verifyTwoFactor(userId: string, token: string): Promise<boolean> {
    const user = await this.userRepository.getUserByUserId(userId);
    if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) {
      return false;
    }

    return speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token,
      window: 2,
    });
  }

  async getTwoFactorStatus(auth: AuthData): Promise<any> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    return {
      enabled: user.twoFactorEnabled,
      hasSecret: !!user.twoFactorSecret,
      hasTempSecret: !!user.tempTwoFactorSecret,
    };
  }

  async generateBackupCodes(auth: AuthData): Promise<string[]> {
    const user = await this.userRepository.getUserByUserId(auth.id.toString());
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (!user.twoFactorEnabled) {
      throw new BadRequestException('Two-factor authentication must be enabled first');
    }

    // Generate 10 backup codes
    const backupCodes = Array.from({ length: 10 }, () => 
      Math.random().toString(36).substring(2, 10).toUpperCase()
    );

    // In a real implementation, you would store these codes securely
    // For now, we'll just return them
    this.logger.log(`Backup codes generated for user ${user.id}`);

    return backupCodes;
  }
}
