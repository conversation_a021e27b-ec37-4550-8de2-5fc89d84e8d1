import { Injectable, UnauthorizedException } from '@nestjs/common';
import { UserRepository } from '../repositories/users.repository';
import { WhitelistedAddressRepository } from '../repositories/whitelisted-address.repository';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';
import { randomUUID } from 'crypto';
import { createHash } from 'crypto';

@Injectable()
export class SecurityService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly whitelistedAddressRepository: WhitelistedAddressRepository,
  ) {}

  async setupTwoFactorAuth(userId: string) {
    const user = await this.userRepository.getUserByUserId(userId);
    
    // Generate new secret
    const secret = speakeasy.generateSecret({
      name: `CredPal:${user.email}`,
    });
    
    // Store secret temporarily (should be confirmed before saving permanently)
    await this.userRepository.update(
      { id: user.id },
      { 
        tempTwoFactorSecret: secret.base32,
        twoFactorEnabled: false,
      },
    );
    
    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);
    
    return {
      secret: secret.base32,
      qrCode: qrCodeUrl,
    };
  }

  async verifyAndEnableTwoFactor(userId: string, token: string) {
    const user = await this.userRepository.getUserByUserId(userId);
    
    if (!user.tempTwoFactorSecret) {
      throw new UnauthorizedException('Two-factor authentication not set up');
    }
    
    const verified = speakeasy.totp.verify({
      secret: user.tempTwoFactorSecret,
      encoding: 'base32',
      token,
    });
    
    if (!verified) {
      throw new UnauthorizedException('Invalid two-factor code');
    }
    
    // Enable 2FA and save the secret permanently
    await this.userRepository.update(
      { id: user.id },
      { 
        twoFactorSecret: user.tempTwoFactorSecret,
        tempTwoFactorSecret: null,
        twoFactorEnabled: true,
      },
    );
    
    return { success: true };
  }

  async verifyTwoFactorToken(userId: string, token: string) {
    const user = await this.userRepository.getUserByUserId(userId);
    
    if (!user.twoFactorEnabled || !user.twoFactorSecret) {
      throw new UnauthorizedException('Two-factor authentication not enabled');
    }
    
    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token,
      window: 1, // Allow 1 period before and after for clock drift
    });
    
    return { verified };
  }

  async addWhitelistedAddress(userId: string, addressData) {
    const user = await this.userRepository.getUserByUserId(userId);
    
    const whitelistedAddress = await this.whitelistedAddressRepository.save({
      user,
      address: addressData.address,
      label: addressData.label,
      currency: addressData.currency,
      isActive: true,
    });
    
    return whitelistedAddress;
  }

  async verifyTransactionSignature(userId: string, transactionData, signature) {
    const user = await this.userRepository.getUserByUserId(userId);
    
    // Create a hash of the transaction data
    const dataString = JSON.stringify(transactionData);
    const dataHash = createHash('sha256').update(dataString).digest('hex');
    
    // Verify the signature (implementation depends on your signature method)
    // This is a placeholder for actual signature verification
    const isValid = this.verifySignature(dataHash, signature, user.publicKey);
    
    if (!isValid) {
      throw new UnauthorizedException('Invalid transaction signature');
    }
    
    return { verified: true };
  }

  private verifySignature(dataHash, signature, publicKey) {
    // Implementation of signature verification
    // This would use a library like crypto or elliptic
    return true; // Placeholder
  }
}
