import { Controller, Post, Get, Body, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard, GetAuthData } from '@crednet/authmanager';
import { AuthData } from '@crednet/authmanager';
import { TwoFactorService, TwoFactorSetupDto, TwoFactorVerifyDto } from './two-factor.service';

@Controller('security/2fa')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('Security - Two Factor Authentication')
export class TwoFactorController {
  constructor(private readonly twoFactorService: TwoFactorService) {}

  @Get('status')
  @ApiOperation({ summary: 'Get two-factor authentication status' })
  @ApiResponse({ status: 200, description: '2FA status retrieved successfully' })
  async getTwoFactorStatus(@GetAuthData() auth: AuthData) {
    return this.twoFactorService.getTwoFactorStatus(auth);
  }

  @Post('generate-secret')
  @ApiOperation({ summary: 'Generate 2FA secret and QR code' })
  @ApiResponse({ status: 200, description: '2FA secret generated successfully' })
  @ApiResponse({ status: 400, description: '2FA already enabled or user not found' })
  async generateSecret(@GetAuthData() auth: AuthData) {
    return this.twoFactorService.generateSecret(auth);
  }

  @Post('enable')
  @ApiOperation({ summary: 'Enable two-factor authentication' })
  @ApiResponse({ status: 200, description: '2FA enabled successfully' })
  @ApiResponse({ status: 400, description: 'Invalid token or 2FA already enabled' })
  async enableTwoFactor(
    @GetAuthData() auth: AuthData,
    @Body() dto: TwoFactorSetupDto,
  ) {
    return this.twoFactorService.enableTwoFactor(auth, dto);
  }

  @Post('disable')
  @ApiOperation({ summary: 'Disable two-factor authentication' })
  @ApiResponse({ status: 200, description: '2FA disabled successfully' })
  @ApiResponse({ status: 400, description: 'Invalid token or 2FA not enabled' })
  async disableTwoFactor(
    @GetAuthData() auth: AuthData,
    @Body() dto: TwoFactorVerifyDto,
  ) {
    return this.twoFactorService.disableTwoFactor(auth, dto);
  }

  @Post('backup-codes')
  @ApiOperation({ summary: 'Generate backup codes for 2FA' })
  @ApiResponse({ status: 200, description: 'Backup codes generated successfully' })
  @ApiResponse({ status: 400, description: '2FA must be enabled first' })
  async generateBackupCodes(@GetAuthData() auth: AuthData) {
    return this.twoFactorService.generateBackupCodes(auth);
  }
}
